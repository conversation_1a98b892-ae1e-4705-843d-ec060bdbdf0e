import React, { useState } from 'react'
import { useAppStore } from '~/store'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '~/components/ui/dialog'

interface AIOptimizerProps {
  isOpen: boolean
  onClose: () => void
  initialContent?: string
  contentType?: 'title' | 'description' | 'tags'
  onOptimized?: (optimizedContent: string) => void
}

export function AIOptimizer({ 
  isOpen, 
  onClose, 
  initialContent = '', 
  contentType = 'description',
  onOptimized 
}: AIOptimizerProps) {
  const { optimizeContent, aiOptimizing, settings } = useAppStore()

  const [content, setContent] = useState(initialContent)
  const [optimizedContent, setOptimizedContent] = useState('')
  const [customPrompt, setCustomPrompt] = useState('')
  const [targetPlatform, setTargetPlatform] = useState('')
  const [tone, setTone] = useState<'professional' | 'casual' | 'marketing'>('professional')
  const [maxLength, setMaxLength] = useState(500)
  const [showComparison, setShowComparison] = useState(false)

  const platforms = [
    { value: '', label: '通用优化' },
    { value: 'producthunt', label: 'Product Hunt' },
    { value: 'hacker_news', label: 'Hacker News' },
    { value: 'reddit', label: 'Reddit' },
    { value: 'twitter', label: 'Twitter' },
    { value: 'linkedin', label: 'LinkedIn' },
    { value: 'github', label: 'GitHub' },
    { value: 'indie_hackers', label: 'Indie Hackers' }
  ]

  const tones = [
    { value: 'professional', label: '专业正式' },
    { value: 'casual', label: '轻松随意' },
    { value: 'marketing', label: '营销推广' }
  ]

  const maxLengthOptions = {
    title: [50, 80, 100, 150],
    description: [200, 300, 500, 1000],
    tags: [100, 150, 200, 300]
  }

  React.useEffect(() => {
    setContent(initialContent)
    setOptimizedContent('')
    setShowComparison(false)
  }, [initialContent, isOpen])

  const handleOptimize = async () => {
    if (!content.trim()) return

    try {
      const context = {
        targetPlatform: targetPlatform || undefined,
        fieldType: contentType,
        maxLength,
        tone,
        customPrompt: customPrompt.trim() || undefined
      }

      const optimized = await optimizeContent(content, context)
      setOptimizedContent(optimized)
      setShowComparison(true)
    } catch (error) {
      // 错误已在store中处理
    }
  }

  const handleAcceptOptimization = () => {
    if (onOptimized && optimizedContent) {
      onOptimized(optimizedContent)
    }
    onClose()
  }

  const handleUseOriginal = () => {
    if (onOptimized) {
      onOptimized(content)
    }
    onClose()
  }

  const getContentTypeLabel = () => {
    switch (contentType) {
      case 'title': return '标题'
      case 'description': return '描述'
      case 'tags': return '标签'
      default: return '内容'
    }
  }

  const isAIAvailable = settings.aiOptimizationEnabled

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>AI内容优化</DialogTitle>
          <DialogDescription>
            使用AI优化您的{getContentTypeLabel()}内容，提高提交质量
          </DialogDescription>
        </DialogHeader>

        {!isAIAvailable ? (
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="py-4">
              <div className="flex items-center gap-2 text-yellow-800">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <span className="font-medium">AI优化功能不可用</span>
              </div>
              <p className="text-sm text-yellow-700 mt-2">
                请检查网络连接或在设置中配置API密钥以启用AI优化功能
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {/* 原始内容输入 */}
            <div>
              <label className="block text-sm font-medium mb-2">
                原始{getContentTypeLabel()}
              </label>
              <textarea
                className="w-full min-h-[120px] px-3 py-2 border border-input bg-background rounded-md text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 resize-vertical"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder={`输入要优化的${getContentTypeLabel()}...`}
              />
              <p className="text-xs text-muted-foreground mt-1">
                {content.length} 字符
              </p>
            </div>

            {/* 优化配置 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  目标平台
                </label>
                <select
                  className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  value={targetPlatform}
                  onChange={(e) => setTargetPlatform(e.target.value)}
                >
                  {platforms.map(platform => (
                    <option key={platform.value} value={platform.value}>
                      {platform.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  内容风格
                </label>
                <select
                  className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  value={tone}
                  onChange={(e) => setTone(e.target.value as any)}
                >
                  {tones.map(toneOption => (
                    <option key={toneOption.value} value={toneOption.value}>
                      {toneOption.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  最大长度
                </label>
                <select
                  className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  value={maxLength}
                  onChange={(e) => setMaxLength(Number(e.target.value))}
                >
                  {maxLengthOptions[contentType].map(length => (
                    <option key={length} value={length}>
                      {length} 字符
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  自定义提示
                </label>
                <Input
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  placeholder="添加特殊要求（可选）"
                />
              </div>
            </div>

            {/* 优化按钮 */}
            <div className="flex justify-center">
              <Button
                onClick={handleOptimize}
                disabled={!content.trim() || aiOptimizing}
                size="lg"
              >
                {aiOptimizing ? (
                  <>
                    <svg className="w-5 h-5 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    AI优化中...
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    开始优化
                  </>
                )}
              </Button>
            </div>

            {/* 优化结果对比 */}
            {showComparison && optimizedContent && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">优化结果对比</h3>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {/* 原始内容 */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base flex items-center gap-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        原始内容
                      </CardTitle>
                      <CardDescription>
                        {content.length} 字符
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="p-3 bg-muted rounded text-sm whitespace-pre-wrap">
                        {content}
                      </div>
                    </CardContent>
                  </Card>

                  {/* 优化后内容 */}
                  <Card className="border-green-200">
                    <CardHeader>
                      <CardTitle className="text-base flex items-center gap-2">
                        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        AI优化后
                      </CardTitle>
                      <CardDescription>
                        {optimizedContent.length} 字符
                        {optimizedContent.length !== content.length && (
                          <span className={`ml-2 ${optimizedContent.length > content.length ? 'text-red-600' : 'text-green-600'}`}>
                            ({optimizedContent.length > content.length ? '+' : ''}{optimizedContent.length - content.length})
                          </span>
                        )}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="p-3 bg-green-50 border border-green-200 rounded text-sm whitespace-pre-wrap">
                        {optimizedContent}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* 优化统计 */}
                <Card>
                  <CardContent className="py-4">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold text-primary">
                          {optimizedContent.split(/\s+/).length}
                        </div>
                        <div className="text-sm text-muted-foreground">单词数</div>
                      </div>
                      
                      <div>
                        <div className="text-2xl font-bold text-primary">
                          {optimizedContent.length}
                        </div>
                        <div className="text-sm text-muted-foreground">字符数</div>
                      </div>
                      
                      <div>
                        <div className="text-2xl font-bold text-primary">
                          {optimizedContent.split(/[.!?。！？]/).length - 1}
                        </div>
                        <div className="text-sm text-muted-foreground">句子数</div>
                      </div>
                      
                      <div>
                        <div className={`text-2xl font-bold ${
                          optimizedContent.length <= maxLength ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {((optimizedContent.length / maxLength) * 100).toFixed(0)}%
                        </div>
                        <div className="text-sm text-muted-foreground">长度使用率</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* 操作按钮 */}
                <div className="flex justify-end gap-3">
                  <Button variant="outline" onClick={handleUseOriginal}>
                    使用原始内容
                  </Button>
                  <Button onClick={handleAcceptOptimization}>
                    使用优化后内容
                  </Button>
                </div>
              </div>
            )}

            {/* 没有优化结果时的操作按钮 */}
            {!showComparison && (
              <div className="flex justify-end gap-3">
                <Button variant="outline" onClick={onClose}>
                  取消
                </Button>
                <Button onClick={handleUseOriginal} disabled={!content.trim()}>
                  直接使用
                </Button>
              </div>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}