import React, { useState, useEffect } from 'react'
import { useAppStore } from '~/store'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Skeleton } from '~/components/ui/skeleton'

interface FormDetectorProps {
  onFormSelected: (form: any) => void
}

export function FormDetector({ onFormSelected }: FormDetectorProps) {
  const { 
    detectedForms, 
    selectedForm, 
    selectForm, 
    detectForms, 
    formDetectionLoading,
    error 
  } = useAppStore()

  const [highlightedFormIndex, setHighlightedFormIndex] = useState<number | null>(null)

  useEffect(() => {
    // 监听来自content script的消息
    const handleMessage = (message: any) => {
      if (message.type === 'PAGE_STRUCTURE_CHANGED') {
        // 页面结构发生变化，建议重新检测
        console.log('页面结构发生变化，建议重新检测表单')
      }
    }

    chrome.runtime.onMessage.addListener(handleMessage)
    return () => chrome.runtime.onMessage.removeListener(handleMessage)
  }, [])

  const handleDetectForms = async () => {
    try {
      await detectForms()
    } catch (error) {
      // 错误已在store中处理
    }
  }

  const handleHighlightForm = async (formIndex: number) => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (!tab.id) return

      await chrome.tabs.sendMessage(tab.id, {
        type: 'HIGHLIGHT_FORM',
        formIndex
      })
      
      setHighlightedFormIndex(formIndex)
    } catch (error) {
      console.error('高亮表单失败:', error)
    }
  }

  const handleClearHighlights = async () => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (!tab.id) return

      await chrome.tabs.sendMessage(tab.id, {
        type: 'CLEAR_HIGHLIGHTS'
      })
      
      setHighlightedFormIndex(null)
    } catch (error) {
      console.error('清除高亮失败:', error)
    }
  }

  const handleSelectForm = (form: any) => {
    selectForm(form)
    onFormSelected(form)
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-50'
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-50'
    return 'text-red-600 bg-red-50'
  }

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 0.8) return '高'
    if (confidence >= 0.6) return '中'
    return '低'
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">表单检测</h2>
        <div className="flex gap-2">
          {highlightedFormIndex !== null && (
            <Button onClick={handleClearHighlights} variant="outline" size="sm">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              清除高亮
            </Button>
          )}
          <Button onClick={handleDetectForms} disabled={formDetectionLoading} size="sm">
            {formDetectionLoading ? (
              <>
                <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                检测中...
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                检测表单
              </>
            )}
          </Button>
        </div>
      </div>

      {error && (
        <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded">
          {error}
        </div>
      )}

      {formDetectionLoading ? (
        <div className="space-y-3">
          {Array.from({ length: 2 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <Skeleton className="h-5 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                  <Skeleton className="h-6 w-12" />
                </div>
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : detectedForms.length === 0 ? (
        <Card>
          <CardContent className="py-8 text-center">
            <div className="text-muted-foreground mb-4">
              <svg className="w-12 h-12 mx-auto text-muted-foreground/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <p className="text-muted-foreground mb-4">尚未检测到可用的表单</p>
            <p className="text-sm text-muted-foreground mb-4">
              请确保当前页面包含项目提交表单，然后点击"检测表单"按钮
            </p>
            <Button onClick={handleDetectForms} variant="outline">
              重新检测
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          <p className="text-sm text-muted-foreground">
            检测到 {detectedForms.length} 个可能的提交表单
          </p>
          
          {detectedForms.map((form, index) => (
            <Card 
              key={index}
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedForm?.index === index ? 'ring-2 ring-primary ring-offset-2' : ''
              } ${
                highlightedFormIndex === index ? 'ring-2 ring-blue-500 ring-offset-2' : ''
              }`}
              onClick={() => handleSelectForm(form)}
            >
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-base flex items-center gap-2">
                      表单 #{index + 1}
                      {form.context?.title && (
                        <span className="text-sm font-normal text-muted-foreground">
                          - {form.context.title}
                        </span>
                      )}
                    </CardTitle>
                    <CardDescription className="flex items-center gap-4">
                      <span>
                        {form.fieldsCount} 个字段
                      </span>
                      {form.hasSubmitButton && (
                        <span className="flex items-center gap-1">
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                          </svg>
                          有提交按钮
                        </span>
                      )}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(form.confidence)}`}>
                      匹配度：{getConfidenceLabel(form.confidence)}
                    </span>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                {form.context?.description && (
                  <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                    {form.context.description}
                  </p>
                )}
                
                <div className="flex items-center justify-between">
                  <div className="flex flex-wrap gap-2">
                    {form.fields.slice(0, 4).map((field: any, fieldIndex: number) => (
                      <span 
                        key={fieldIndex}
                        className="inline-flex items-center px-2 py-1 rounded text-xs bg-secondary text-secondary-foreground"
                      >
                        {field.label || field.name || field.placeholder || field.type}
                      </span>
                    ))}
                    {form.fields.length > 4 && (
                      <span className="text-xs text-muted-foreground">
                        +{form.fields.length - 4} 个字段
                      </span>
                    )}
                  </div>

                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleHighlightForm(index)
                      }}
                      className={`h-8 px-2 text-xs ${
                        highlightedFormIndex === index ? 'bg-blue-100 text-blue-600' : ''
                      }`}
                    >
                      {highlightedFormIndex === index ? '已高亮' : '高亮显示'}
                    </Button>
                  </div>
                </div>

                {form.context?.action && (
                  <div className="mt-2 text-xs text-muted-foreground">
                    提交到: {form.context.action}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}