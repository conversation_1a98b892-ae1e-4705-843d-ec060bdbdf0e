import type { PlasmoCSConfig } from "plasmo"
import { FormDetector } from "~services/form"
import type { FormElement } from "~types"

export const config: PlasmoCSConfig = {
  matches: ["https://*/*"],
  all_frames: true
}

class ContentFormDetector {
  private detector: FormDetector
  private detectedForms: FormElement[] = []
  private highlightedElements: Set<HTMLElement> = new Set()
  private isActive = false

  constructor() {
    this.detector = new FormDetector()
    this.initializeMessageHandlers()
  }

  private initializeMessageHandlers(): void {
    // 监听来自侧边栏的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      switch (message.type) {
        case 'DETECT_FORMS':
          this.handleDetectForms()
            .then(forms => sendResponse({ success: true, forms }))
            .catch(error => sendResponse({ success: false, error: error.message }))
          return true // 异步响应

        case 'HIGHLIGHT_FORM':
          this.handleHighlightForm(message.formIndex)
          sendResponse({ success: true })
          break

        case 'CLEAR_HIGHLIGHTS':
          this.handleClearHighlights()
          sendResponse({ success: true })
          break

        case 'GET_FORM_CONTEXT':
          this.handleGetFormContext(message.formIndex)
            .then(context => sendResponse({ success: true, context }))
            .catch(error => sendResponse({ success: false, error: error.message }))
          return true

        case 'FILL_FORM':
          this.handleFillForm(message.formIndex, message.project, message.mapping)
            .then(result => sendResponse({ success: true, result }))
            .catch(error => sendResponse({ success: false, error: error.message }))
          return true

        default:
          sendResponse({ success: false, error: 'Unknown message type' })
      }
    })
  }

  private async handleDetectForms(): Promise<any[]> {
    try {
      console.log('开始检测表单...')
      
      // 等待页面完全加载
      if (document.readyState !== 'complete') {
        await new Promise(resolve => {
          if (document.readyState === 'complete') {
            resolve(void 0)
          } else {
            window.addEventListener('load', resolve, { once: true })
          }
        })
      }

      this.detectedForms = this.detector.detectForms()
      console.log(`检测到 ${this.detectedForms.length} 个表单`)

      // 为每个表单添加序列化信息
      const serializedForms = this.detectedForms.map((form, index) => ({
        index,
        confidence: form.confidence,
        fieldsCount: form.fields.length,
        hasSubmitButton: !!form.submitButton,
        context: this.detector.getFormContext(form.element),
        fields: form.fields.map(field => ({
          name: field.name,
          type: field.type,
          label: field.label,
          placeholder: field.placeholder,
          required: field.required,
          maxLength: field.maxLength
        }))
      }))

      return serializedForms
    } catch (error) {
      console.error('表单检测失败:', error)
      throw error
    }
  }

  private handleHighlightForm(formIndex: number): void {
    this.clearHighlights()

    if (formIndex >= 0 && formIndex < this.detectedForms.length) {
      const form = this.detectedForms[formIndex]
      
      // 高亮表单
      this.highlightElement(form.element, 'form')
      
      // 高亮表单字段
      form.fields.forEach(field => {
        this.highlightElement(field.element, 'field')
      })

      // 高亮提交按钮
      if (form.submitButton) {
        this.highlightElement(form.submitButton, 'submit')
      }

      this.isActive = true
    }
  }

  private handleClearHighlights(): void {
    this.clearHighlights()
    this.isActive = false
  }

  private async handleGetFormContext(formIndex: number): Promise<any> {
    if (formIndex >= 0 && formIndex < this.detectedForms.length) {
      const form = this.detectedForms[formIndex]
      return this.detector.getFormContext(form.element)
    }
    throw new Error('表单索引无效')
  }

  private async handleFillForm(formIndex: number, project: any, mapping?: any): Promise<any> {
    if (formIndex < 0 || formIndex >= this.detectedForms.length) {
      throw new Error('表单索引无效')
    }

    const form = this.detectedForms[formIndex]
    
    // 动态导入FormFiller（避免在content script中直接导入大模块）
    const { FormFiller } = await import('~services/form')
    const filler = new FormFiller()

    try {
      const result = await filler.fillForm(form, project, mapping)
      
      // 如果填充成功，添加视觉反馈
      if (result.success) {
        this.showFillSuccess(form.element)
      }

      return result
    } catch (error) {
      console.error('表单填充失败:', error)
      throw error
    }
  }

  private highlightElement(element: HTMLElement, type: 'form' | 'field' | 'submit'): void {
    // 创建高亮样式
    const originalStyle = element.style.cssText
    element.setAttribute('data-original-style', originalStyle)

    let highlightStyle = ''
    switch (type) {
      case 'form':
        highlightStyle = `
          outline: 3px solid #3b82f6 !important;
          outline-offset: 2px !important;
          background-color: rgba(59, 130, 246, 0.1) !important;
        `
        break
      case 'field':
        highlightStyle = `
          outline: 2px solid #10b981 !important;
          outline-offset: 1px !important;
          background-color: rgba(16, 185, 129, 0.1) !important;
        `
        break
      case 'submit':
        highlightStyle = `
          outline: 2px solid #f59e0b !important;
          outline-offset: 1px !important;
          background-color: rgba(245, 158, 11, 0.1) !important;
        `
        break
    }

    element.style.cssText += highlightStyle
    element.setAttribute('data-linktrackpro-highlight', type)
    this.highlightedElements.add(element)

    // 添加点击事件监听
    element.addEventListener('click', this.handleHighlightClick, { once: true })
  }

  private handleHighlightClick = (event: Event): void => {
    if (this.isActive) {
      event.preventDefault()
      event.stopPropagation()
      
      const element = event.target as HTMLElement
      const type = element.getAttribute('data-linktrackpro-highlight')
      
      // 发送点击事件到侧边栏
      chrome.runtime.sendMessage({
        type: 'FORM_ELEMENT_CLICKED',
        elementType: type,
        elementInfo: {
          tagName: element.tagName,
          name: (element as HTMLInputElement).name || element.id,
          type: (element as HTMLInputElement).type
        }
      })
    }
  }

  private clearHighlights(): void {
    this.highlightedElements.forEach(element => {
      // 恢复原始样式
      const originalStyle = element.getAttribute('data-original-style')
      if (originalStyle !== null) {
        element.style.cssText = originalStyle
        element.removeAttribute('data-original-style')
      }
      
      element.removeAttribute('data-linktrackpro-highlight')
    })
    
    this.highlightedElements.clear()
  }

  private showFillSuccess(form: HTMLFormElement): void {
    // 创建成功提示
    const successBadge = document.createElement('div')
    successBadge.style.cssText = `
      position: absolute;
      top: -10px;
      right: -10px;
      background: #10b981;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      z-index: 10000;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    `
    successBadge.textContent = '✓ 填充完成'

    // 添加到表单
    form.style.position = form.style.position || 'relative'
    form.appendChild(successBadge)

    // 3秒后移除
    setTimeout(() => {
      if (successBadge.parentNode) {
        successBadge.parentNode.removeChild(successBadge)
      }
    }, 3000)
  }

  // 页面变化监听
  private observePageChanges(): void {
    const observer = new MutationObserver((mutations) => {
      let shouldRedetect = false

      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element
              if (element.tagName === 'FORM' || element.querySelector('form')) {
                shouldRedetect = true
              }
            }
          })
        }
      })

      if (shouldRedetect) {
        // 通知侧边栏页面结构发生变化
        chrome.runtime.sendMessage({
          type: 'PAGE_STRUCTURE_CHANGED'
        })
      }
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })
  }
}

// 初始化内容脚本
const contentDetector = new ContentFormDetector()

// 页面加载完成后开始监听变化
if (document.readyState === 'complete') {
  contentDetector['observePageChanges']()
} else {
  window.addEventListener('load', () => {
    contentDetector['observePageChanges']()
  })
}

// 导出空组件（Plasmo要求）
export default function FormDetectorContent() {
  return null
}