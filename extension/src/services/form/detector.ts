import type { 
  FormElement, 
  FormField, 
  FormDetector as IFormDetector 
} from '~/types'

export class FormDetector implements IFormDetector {
  private readonly SUBMISSION_KEYWORDS = [
    'submit', 'send', 'post', 'create', 'add', 'register', 'signup', 'apply',
    'contact', 'feedback', 'message', 'inquiry', 'request', 'upload'
  ]

  private readonly FIELD_KEYWORDS = {
    name: ['name', 'title', 'app', 'product', 'project', 'software'],
    email: ['email', 'mail', 'contact'],
    url: ['url', 'link', 'website', 'site', 'homepage', 'demo'],
    description: ['description', 'desc', 'about', 'detail', 'info', 'summary', 'content', 'message'],
    category: ['category', 'type', 'tag', 'genre', 'classification'],
    tags: ['tags', 'keywords', 'labels']
  }

  private readonly MIN_CONFIDENCE_SCORE = 0.3

  detectForms(): FormElement[] {
    const forms = Array.from(document.querySelectorAll('form'))
    const detectedForms: FormElement[] = []

    for (const formElement of forms) {
      try {
        const fields = this.analyzeFormFields({ element: formElement } as FormElement)
        
        if (fields.length === 0) continue

        const submitButton = this.findSubmitButton(formElement)
        const confidence = this.calculateConfidence(formElement, fields, submitButton)

        if (confidence >= this.MIN_CONFIDENCE_SCORE) {
          detectedForms.push({
            element: formElement,
            fields,
            submitButton,
            confidence
          })
        }
      } catch (error) {
        console.warn('表单分析失败:', error)
      }
    }

    // 按置信度排序
    return detectedForms.sort((a, b) => b.confidence - a.confidence)
  }

  analyzeFormFields(form: FormElement): FormField[] {
    const inputs = Array.from(form.element.querySelectorAll('input, textarea, select'))
    const fields: FormField[] = []

    for (const element of inputs) {
      try {
        const field = this.analyzeField(element)
        if (field) {
          fields.push(field)
        }
      } catch (error) {
        console.warn('字段分析失败:', error)
      }
    }

    return fields
  }

  isSubmissionForm(form: FormElement): boolean {
    // 检查表单是否可能是提交表单
    const confidence = this.calculateConfidence(form.element, form.fields, form.submitButton)
    return confidence >= this.MIN_CONFIDENCE_SCORE
  }

  private analyzeField(element: Element): FormField | null {
    const input = element as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement

    // 跳过隐藏字段和特殊类型
    if (this.shouldSkipField(input)) {
      return null
    }

    const fieldType = this.determineFieldType(input)
    const name = this.extractFieldName(input)
    const label = this.extractFieldLabel(input)
    const placeholder = this.extractFieldPlaceholder(input)
    const required = this.isFieldRequired(input)
    const maxLength = this.extractMaxLength(input)

    return {
      element: input,
      type: fieldType,
      name,
      label,
      placeholder,
      required,
      maxLength
    }
  }

  private shouldSkipField(input: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement): boolean {
    const type = (input as HTMLInputElement).type?.toLowerCase()
    const style = window.getComputedStyle(input)

    // 跳过隐藏字段
    if (style.display === 'none' || style.visibility === 'hidden') {
      return true
    }

    // 跳过特殊输入类型
    const skipTypes = ['hidden', 'submit', 'button', 'reset', 'image', 'checkbox', 'radio']
    if (type && skipTypes.includes(type)) {
      return true
    }

    // 跳过密码字段
    if (type === 'password') {
      return true
    }

    return false
  }

  private determineFieldType(input: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement): FormField['type'] {
    if (input.tagName.toLowerCase() === 'textarea') {
      return 'textarea'
    }

    if (input.tagName.toLowerCase() === 'select') {
      return 'select'
    }

    const htmlInput = input as HTMLInputElement
    const type = htmlInput.type?.toLowerCase()

    switch (type) {
      case 'email':
        return 'email'
      case 'url':
        return 'url'
      case 'file':
        return 'file'
      default:
        return 'text'
    }
  }

  private extractFieldName(input: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement): string {
    return input.name || input.id || this.generateFieldName(input)
  }

  private extractFieldLabel(input: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement): string | undefined {
    // 查找关联的label标签
    let label: HTMLLabelElement | null = null

    if (input.id) {
      label = document.querySelector(`label[for="${input.id}"]`)
    }

    if (!label) {
      // 查找父级label
      let parent = input.parentElement
      while (parent && parent.tagName.toLowerCase() !== 'form') {
        if (parent.tagName.toLowerCase() === 'label') {
          label = parent as HTMLLabelElement
          break
        }
        parent = parent.parentElement
      }
    }

    if (!label) {
      // 查找前一个文本节点或元素
      label = this.findNearbyLabel(input)
    }

    return label?.textContent?.trim() || undefined
  }

  private extractFieldPlaceholder(input: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement): string | undefined {
    const htmlInput = input as HTMLInputElement
    return htmlInput.placeholder?.trim() || undefined
  }

  private isFieldRequired(input: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement): boolean {
    return input.required || input.hasAttribute('required')
  }

  private extractMaxLength(input: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement): number | undefined {
    const htmlInput = input as HTMLInputElement | HTMLTextAreaElement
    return htmlInput.maxLength > 0 ? htmlInput.maxLength : undefined
  }

  private findSubmitButton(form: HTMLFormElement): HTMLButtonElement | undefined {
    // 查找submit按钮
    const buttons = Array.from(form.querySelectorAll('button, input[type="submit"]'))
    
    for (const button of buttons) {
      const htmlButton = button as HTMLButtonElement
      const type = htmlButton.type?.toLowerCase()
      
      if (type === 'submit' || !type) {
        return htmlButton
      }
    }

    // 查找可能的提交按钮（基于文本内容）
    const allButtons = Array.from(form.querySelectorAll('button, input[type="button"]'))
    
    for (const button of allButtons) {
      const text = button.textContent?.toLowerCase() || (button as HTMLInputElement).value?.toLowerCase() || ''
      
      if (this.SUBMISSION_KEYWORDS.some(keyword => text.includes(keyword))) {
        return button as HTMLButtonElement
      }
    }

    return undefined
  }

  private calculateConfidence(
    form: HTMLFormElement, 
    fields: FormField[], 
    submitButton?: HTMLButtonElement
  ): number {
    let score = 0
    let maxScore = 0

    // 基础分数：有字段
    if (fields.length > 0) {
      score += 0.2
    }
    maxScore += 0.2

    // 有提交按钮
    if (submitButton) {
      score += 0.2
    }
    maxScore += 0.2

    // 字段类型匹配度
    const fieldTypeScore = this.calculateFieldRelevance(fields)
    score += fieldTypeScore * 0.4
    maxScore += 0.4

    // 表单结构分析
    const structureScore = this.analyzeFormStructure(form, fields)
    score += structureScore * 0.2
    maxScore += 0.2

    return maxScore > 0 ? score / maxScore : 0
  }

  private calculateFieldRelevance(fields: FormField[]): number {
    let relevantFields = 0
    const totalFields = fields.length

    for (const field of fields) {
      if (this.isRelevantField(field)) {
        relevantFields++
      }
    }

    return totalFields > 0 ? relevantFields / totalFields : 0
  }

  private isRelevantField(field: FormField): boolean {
    const searchText = `${field.name} ${field.label || ''} ${field.placeholder || ''}`.toLowerCase()

    // 检查是否匹配项目提交相关的字段
    for (const [category, keywords] of Object.entries(this.FIELD_KEYWORDS)) {
      if (keywords.some(keyword => searchText.includes(keyword))) {
        return true
      }
    }

    return false
  }

  private analyzeFormStructure(form: HTMLFormElement, fields: FormField[]): number {
    let score = 0

    // 表单有合理数量的字段
    if (fields.length >= 3 && fields.length <= 20) {
      score += 0.3
    }

    // 有必填字段
    if (fields.some(field => field.required)) {
      score += 0.2
    }

    // 有长文本字段（描述类）
    if (fields.some(field => field.type === 'textarea')) {
      score += 0.2
    }

    // 有URL字段
    if (fields.some(field => field.type === 'url' || field.name.toLowerCase().includes('url'))) {
      score += 0.3
    }

    return Math.min(score, 1.0)
  }

  private findNearbyLabel(input: Element): HTMLLabelElement | null {
    // 查找前面的兄弟元素
    let sibling = input.previousElementSibling
    while (sibling) {
      if (sibling.tagName.toLowerCase() === 'label') {
        return sibling as HTMLLabelElement
      }
      
      // 查找包含文本的元素
      if (sibling.textContent?.trim()) {
        const text = sibling.textContent.trim()
        if (text.length > 0 && text.length < 100) {
          // 创建虚拟label元素
          const virtualLabel = document.createElement('label')
          virtualLabel.textContent = text
          return virtualLabel
        }
      }
      
      sibling = sibling.previousElementSibling
    }

    return null
  }

  private generateFieldName(input: Element): string {
    // 基于元素属性生成字段名
    const classList = Array.from(input.classList).join(' ')
    const placeholder = (input as HTMLInputElement).placeholder || ''
    
    if (classList) {
      return `field_${classList.replace(/\s+/g, '_')}`
    }
    
    if (placeholder) {
      return `field_${placeholder.replace(/\s+/g, '_').toLowerCase()}`
    }
    
    return `field_${Date.now()}`
  }

  // 高级检测方法
  getFormContext(form: HTMLFormElement): {
    action?: string
    method?: string
    enctype?: string
    title?: string
    description?: string
  } {
    const action = form.action
    const method = form.method?.toLowerCase() || 'get'
    const enctype = form.enctype
    
    // 尝试从表单周围的内容提取标题和描述
    const title = this.extractFormTitle(form)
    const description = this.extractFormDescription(form)

    return {
      action: action || undefined,
      method,
      enctype: enctype || undefined,
      title,
      description
    }
  }

  private extractFormTitle(form: HTMLFormElement): string | undefined {
    // 查找表单上方的标题元素
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
    
    for (const heading of headings) {
      if (this.isElementNearForm(heading, form)) {
        const text = heading.textContent?.trim()
        if (text && text.length < 200) {
          return text
        }
      }
    }

    return undefined
  }

  private extractFormDescription(form: HTMLFormElement): string | undefined {
    // 查找表单相关的描述文本
    const descriptions = document.querySelectorAll('p, div, span')
    
    for (const desc of descriptions) {
      if (this.isElementNearForm(desc, form)) {
        const text = desc.textContent?.trim()
        if (text && text.length > 20 && text.length < 500) {
          return text
        }
      }
    }

    return undefined
  }

  private isElementNearForm(element: Element, form: HTMLFormElement): boolean {
    // 检查元素是否在表单附近
    const elementRect = element.getBoundingClientRect()
    const formRect = form.getBoundingClientRect()
    
    // 垂直距离检查
    const verticalDistance = Math.abs(elementRect.bottom - formRect.top)
    
    return verticalDistance < 200 // 200px内认为是相关的
  }
}