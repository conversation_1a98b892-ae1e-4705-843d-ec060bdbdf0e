import type { 
  Project, 
  CreateProjectDto, 
  ExternalLink, 
  CreateLinkDto, 
  FormElement, 
  FormField, 
  FieldMapping, 
  FillResult,
  OptimizationContext,
  AIOptimizationResult,
  UserSettings
} from '~/types'

// 项目管理服务接口
export interface ProjectManager {
  getProjects(): Promise<Project[]>
  addProject(project: CreateProjectDto): Promise<Project>
  updateProject(id: string, updates: Partial<Project>): Promise<Project>
  deleteProject(id: string): Promise<void>
  syncWithAPI(): Promise<void>
}

// 外链管理服务接口
export interface LinkManager {
  getLinks(): Promise<ExternalLink[]>
  addLink(link: CreateLinkDto): Promise<ExternalLink>
  updateLink(id: string, updates: Partial<ExternalLink>): Promise<ExternalLink>
  deleteLink(id: string): Promise<void>
  saveCurrentUrl(url: string): Promise<ExternalLink>
  normalizeUrl(url: string): string
  syncWithAPI(): Promise<void>
}

// 表单检测服务接口
export interface FormDetector {
  detectForms(): FormElement[]
  analyzeFormFields(form: FormElement): FormField[]
  isSubmissionForm(form: FormElement): boolean
}

// 表单填充服务接口
export interface FormFiller {
  fillForm(form: FormElement, project: Project, mapping?: FieldMapping): Promise<FillResult>
  createFieldMapping(formFields: FormField[], project: Project): FieldMapping
  validateFillResult(result: FillResult): boolean
}

// AI服务接口
export interface AIService {
  optimizeContent(content: string, context: OptimizationContext): Promise<string>
  generateDescription(project: Project, targetPlatform?: string): Promise<string>
  suggestTags(project: Project): Promise<string[]>
  isAvailable(): boolean
  generateOptimizationId(): string
}

// 设置管理服务接口
export interface SettingsManager {
  getSettings(): Promise<UserSettings>
  updateSettings(updates: Partial<UserSettings>): Promise<UserSettings>
  validateApiKey(apiKey: string): Promise<boolean>
  resetSettings(): Promise<void>
}

// 存储服务抽象接口
export interface StorageService {
  // 项目操作
  getProjects(): Promise<Project[]>
  addProject(project: CreateProjectDto): Promise<Project>
  updateProject(id: string, updates: Partial<Project>): Promise<Project>
  deleteProject(id: string): Promise<void>
  
  // 外链操作
  getLinks(): Promise<ExternalLink[]>
  addLink(link: CreateLinkDto): Promise<ExternalLink>
  updateLink(id: string, updates: Partial<ExternalLink>): Promise<ExternalLink>
  deleteLink(id: string): Promise<void>
  
  // 设置操作
  getSettings(): Promise<UserSettings>
  updateSettings(updates: Partial<UserSettings>): Promise<UserSettings>
  
  // 同步操作
  syncWithAPI(): Promise<void>
  isOnline(): boolean
}