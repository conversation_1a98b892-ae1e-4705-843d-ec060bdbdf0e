import type { UserSettings } from '~/types'
import { Storage } from '@plasmohq/storage'
import { StorageFactory } from '../storage/factory'
import { getDefaultApiUrl } from '~/lib/config'

interface APIKeyConfig {
  apiKey: string
  apiUrl?: string
}

export class SettingsManager {
  private storage: Storage
  private storageFactory: StorageFactory

  constructor() {
    this.storage = new Storage({ area: 'local' })
    this.storageFactory = StorageFactory.getInstance()
  }

  // 获取设置
  async getSettings(): Promise<UserSettings> {
    try {
      const storageService = await this.storageFactory.getStorageService()
      return await storageService.getSettings()
    } catch (error) {
      console.error('获取设置失败:', error)
      return this.getDefaultSettings()
    }
  }

  // 更新设置
  async updateSettings(updates: Partial<UserSettings>): Promise<UserSettings> {
    try {
      const storageService = await this.storageFactory.getStorageService()
      return await storageService.updateSettings(updates)
    } catch (error) {
      console.error('更新设置失败:', error)
      throw error
    }
  }

  // 获取默认设置
  private getDefaultSettings(): UserSettings {
    return {
      isPaidUser: false,
      theme: 'system',
      language: 'zh-CN',
      autoOpenSidebar: false,
      aiOptimizationEnabled: false
    }
  }

  // API Key 配置
  async setApiKey(config: APIKeyConfig): Promise<boolean> {
    try {
      const apiUrl = config.apiUrl || getDefaultApiUrl()
      
      // 创建新的存储服务
      await this.storageFactory.switchToAPIStorage({
        apiKey: config.apiKey,
        apiUrl
      })

      // 更新用户类型
      await this.updateSettings({ isPaidUser: true, aiOptimizationEnabled: true })
      
      return true
    } catch (error) {
      console.error('设置API Key失败:', error)
      return false
    }
  }

  // 移除API Key配置
  async removeApiKey(): Promise<void> {
    try {
      // 切换到本地存储
      await this.storageFactory.switchToLocalStorage()
      
      // 更新用户类型
      await this.updateSettings({ isPaidUser: false, aiOptimizationEnabled: false })
    } catch (error) {
      console.error('移除API Key失败:', error)
      throw error
    }
  }

  // 获取API配置
  async getApiConfig(): Promise<{ apiKey?: string; apiUrl?: string } | null> {
    try {
      return await this.storage.get('apiConfig')
    } catch (error) {
      console.error('获取API配置失败:', error)
      return null
    }
  }

  // 验证API Key
  async validateApiKey(apiKey: string, apiUrl?: string): Promise<boolean> {
    const url = apiUrl || getDefaultApiUrl()
    
    try {
      const response = await fetch(`${url}/auth/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        }
      })

      return response.ok
    } catch (error) {
      console.error('验证API Key失败:', error)
      return false
    }
  }

  // 数据迁移：从本地到API
  async migrateToAPI(config: APIKeyConfig): Promise<void> {
    try {
      const apiUrl = config.apiUrl || getDefaultApiUrl()
      await this.storageFactory.migrateFromLocalToAPI({
        apiKey: config.apiKey,
        apiUrl
      })
    } catch (error) {
      console.error('数据迁移失败:', error)
      throw error
    }
  }

  // 数据迁移：从API到本地
  async migrateToLocal(): Promise<void> {
    try {
      await this.storageFactory.migrateFromAPIToLocal()
    } catch (error) {
      console.error('数据迁移失败:', error)
      throw error
    }
  }

  // 重置所有配置
  async resetSettings(): Promise<void> {
    try {
      await this.storageFactory.resetStorage()
      await this.storage.clear()
    } catch (error) {
      console.error('重置设置失败:', error)
      throw error
    }
  }

  // 导出数据
  async exportData(): Promise<any> {
    try {
      const storageService = await this.storageFactory.getStorageService()
      
      const [projects, links, settings] = await Promise.all([
        storageService.getProjects(),
        storageService.getLinks(),
        storageService.getSettings()
      ])

      return {
        projects,
        links,
        settings,
        exportedAt: new Date().toISOString(),
        version: '1.0.0'
      }
    } catch (error) {
      console.error('导出数据失败:', error)
      throw error
    }
  }

  // 导入数据
  async importData(data: any): Promise<void> {
    try {
      // 验证数据格式
      if (!data.projects || !data.links || !data.settings) {
        throw new Error('数据格式不正确')
      }

      const storageService = await this.storageFactory.getStorageService()

      // 清空现有数据（可选）
      // await this.resetSettings()

      // 导入项目
      for (const project of data.projects) {
        const { id, createdAt, updatedAt, ...projectData } = project
        await storageService.addProject(projectData)
      }

      // 导入外链
      for (const link of data.links) {
        const { id, createdAt, updatedAt, ...linkData } = link
        await storageService.addLink(linkData)
      }

      // 导入设置
      await storageService.updateSettings(data.settings)

      console.log('数据导入完成')
    } catch (error) {
      console.error('导入数据失败:', error)
      throw error
    }
  }
}