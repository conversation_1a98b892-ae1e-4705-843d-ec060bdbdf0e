import type { 
  Project, 
  CreateProjectDto, 
  ExternalLink, 
  CreateLinkDto, 
  UserSettings 
} from '~/types'
import { BaseStorageService } from './base'
import { getAppConfig, formatApiUrl, validateApiUrl } from '~/lib/config'

interface APIConfig {
  baseUrl: string
  apiKey: string
  timeout: number
}

export class APIStorageService extends BaseStorageService {
  protected storageType = 'api' as const
  private config: APIConfig
  private retryAttempts: number
  private retryDelay: number

  constructor(config: APIConfig) {
    super()
    
    // 获取全局配置
    const appConfig = getAppConfig()
    
    // 验证并格式化URL
    if (!validateApiUrl(config.baseUrl)) {
      throw new Error(`无效的API URL: ${config.baseUrl}`)
    }
    
    this.config = {
      ...config,
      baseUrl: formatApiUrl(config.baseUrl),
      timeout: config.timeout || appConfig.timeout
    }
    
    this.retryAttempts = appConfig.retryAttempts
    this.retryDelay = appConfig.retryDelay
  }

  // HTTP请求工具方法
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.config.baseUrl}${endpoint}`
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.config.apiKey}`,
      ...options.headers
    }

    let lastError: Error
    
    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout)

        const response = await fetch(url, {
          ...options,
          headers,
          signal: controller.signal
        })

        clearTimeout(timeoutId)

        if (!response.ok) {
          const errorText = await response.text()
          throw new Error(`API请求失败 (${response.status}): ${errorText}`)
        }

        return (await response.json()) as T
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('未知错误')
        
        if (attempt < this.retryAttempts && this.shouldRetry(error)) {
          await this.delay(this.retryDelay * attempt)
          continue
        }
        
        break
      }
    }

    throw lastError!
  }

  private shouldRetry(error: any): boolean {
    // 网络错误或5xx服务器错误才重试
    return error.name === 'AbortError' || 
           error.message.includes('NetworkError') ||
           error.message.includes('5')
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // 项目操作
  async getProjects(): Promise<Project[]> {
    try {
      const response = await this.request<{projects: any[]}>('/projects')
      return response.projects.map(p => this.deserializeProject(p))
    } catch (error) {
      console.error('获取项目列表失败:', error)
      throw error
    }
  }

  async addProject(project: CreateProjectDto): Promise<Project> {
    this.validateProject(project)

    try {
      const response = await this.request<{project: any}>('/projects', {
        method: 'POST',
        body: JSON.stringify(project)
      })
      
      return this.deserializeProject(response.project)
    } catch (error) {
      console.error('添加项目失败:', error)
      throw error
    }
  }

  async updateProject(id: string, updates: Partial<Project>): Promise<Project> {
    try {
      const response = await this.request<{project: any}>(`/projects/${id}`, {
        method: 'PUT',
        body: JSON.stringify(updates)
      })
      
      return this.deserializeProject(response.project)
    } catch (error) {
      console.error('更新项目失败:', error)
      throw error
    }
  }

  async deleteProject(id: string): Promise<void> {
    try {
      await this.request(`/projects/${id}`, {
        method: 'DELETE'
      })
    } catch (error) {
      console.error('删除项目失败:', error)
      throw error
    }
  }

  // 外链操作
  async getLinks(): Promise<ExternalLink[]> {
    try {
      const response = await this.request<{links: any[]}>('/links')
      return response.links.map(l => this.deserializeLink(l))
    } catch (error) {
      console.error('获取外链列表失败:', error)
      throw error
    }
  }

  async addLink(link: CreateLinkDto): Promise<ExternalLink> {
    this.validateLink(link)

    try {
      const response = await this.request<{link: any}>('/links', {
        method: 'POST',
        body: JSON.stringify(link)
      })
      
      return this.deserializeLink(response.link)
    } catch (error) {
      console.error('添加外链失败:', error)
      throw error
    }
  }

  async updateLink(id: string, updates: Partial<ExternalLink>): Promise<ExternalLink> {
    try {
      const response = await this.request<{link: any}>(`/links/${id}`, {
        method: 'PUT',
        body: JSON.stringify(updates)
      })
      
      return this.deserializeLink(response.link)
    } catch (error) {
      console.error('更新外链失败:', error)
      throw error
    }
  }

  async deleteLink(id: string): Promise<void> {
    try {
      await this.request(`/links/${id}`, {
        method: 'DELETE'
      })
    } catch (error) {
      console.error('删除外链失败:', error)
      throw error
    }
  }

  // 设置操作
  async getSettings(): Promise<UserSettings> {
    try {
      const response = await this.request<{settings: UserSettings}>('/settings')
      return response.settings
    } catch (error) {
      console.error('获取设置失败:', error)
      // 返回默认设置
      return {
        isPaidUser: true, // API用户默认为付费用户
        theme: 'system',
        language: 'zh-CN',
        autoOpenSidebar: false,
        aiOptimizationEnabled: true
      }
    }
  }

  async updateSettings(updates: Partial<UserSettings>): Promise<UserSettings> {
    try {
      const response = await this.request<{settings: UserSettings}>('/settings', {
        method: 'PUT',
        body: JSON.stringify(updates)
      })
      
      return response.settings
    } catch (error) {
      console.error('更新设置失败:', error)
      throw error
    }
  }

  // 同步操作
  async syncWithAPI(): Promise<void> {
    // API存储服务本身就是与API同步的，所以这里不需要额外操作
    return Promise.resolve()
  }

  isOnline(): boolean {
    return navigator.onLine
  }

  // API认证相关
  static async validateApiKey(apiKey: string, baseUrl: string): Promise<boolean> {
    try {
      const response = await fetch(`${baseUrl}/auth/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        }
      })

      return response.ok
    } catch (error) {
      console.error('API Key验证失败:', error)
      return false
    }
  }

  // 获取用户信息
  async getUserInfo(): Promise<{isPaidUser: boolean, plan: string}> {
    try {
      const response = await this.request<{user: {isPaidUser: boolean, plan: string}}>('/auth/me')
      return response.user
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return { isPaidUser: false, plan: 'free' }
    }
  }

  // AI优化服务
  async optimizeContent(content: string, context: any): Promise<string> {
    try {
      const response = await this.request<{optimized: string}>('/optimize', {
        method: 'POST',
        body: JSON.stringify({ content, context })
      })
      
      return response.optimized
    } catch (error) {
      console.error('内容优化失败:', error)
      throw error
    }
  }
}