import type { StorageService, UserSettings } from '~/types'
import { LocalStorageService } from './local'
import { APIStorageService } from './api'
import { Storage } from '@plasmohq/storage'
import { getDefaultApiUrl, getAppConfig } from '~/lib/config'

interface StorageConfig {
  apiUrl?: string
  apiKey?: string
  timeout?: number
}

export class StorageFactory {
  private static instance: StorageFactory
  private currentService: StorageService | null = null
  private storage: Storage

  private constructor() {
    this.storage = new Storage({ area: 'local' })
  }

  static getInstance(): StorageFactory {
    if (!StorageFactory.instance) {
      StorageFactory.instance = new StorageFactory()
    }
    return StorageFactory.instance
  }

  async createStorageService(config?: StorageConfig): Promise<StorageService> {
    // 如果已有实例且配置未变，直接返回
    if (this.currentService) {
      return this.currentService
    }

    const userType = await this.detectUserType(config)
    const appConfig = getAppConfig()
    
    if (userType === 'paid' && config?.apiKey) {
      // 使用提供的URL或默认URL
      const apiUrl = config.apiUrl || getDefaultApiUrl()
      
      // 验证API Key
      const isValidKey = await APIStorageService.validateApiKey(config.apiKey, apiUrl)
      
      if (isValidKey) {
        this.currentService = new APIStorageService({
          baseUrl: apiUrl,
          apiKey: config.apiKey,
          timeout: config.timeout || appConfig.timeout
        })
        
        // 保存API配置
        await this.storage.set('apiConfig', {
          apiUrl,
          apiKey: config.apiKey,
          timeout: config.timeout || appConfig.timeout
        })
        
        return this.currentService
      } else {
        console.warn('API Key验证失败，切换到本地存储')
      }
    }

    // 默认使用本地存储
    this.currentService = new LocalStorageService()
    return this.currentService
  }

  async getStorageService(): Promise<StorageService> {
    if (this.currentService) {
      return this.currentService
    }

    // 尝试从存储中恢复API配置
    const savedConfig = await this.storage.get('apiConfig')
    if (savedConfig && savedConfig.apiKey && savedConfig.apiUrl) {
      return this.createStorageService(savedConfig)
    }

    // 使用本地存储
    return this.createStorageService()
  }

  private async detectUserType(config?: StorageConfig): Promise<'free' | 'paid'> {
    // 检查是否提供了API配置
    if (config?.apiKey && config?.apiUrl) {
      return 'paid'
    }

    // 检查本地存储的配置
    const savedConfig = await this.storage.get('apiConfig')
    if (savedConfig?.apiKey && savedConfig?.apiUrl) {
      return 'paid'
    }

    return 'free'
  }

  async switchToLocalStorage(): Promise<StorageService> {
    this.currentService = new LocalStorageService()
    await this.storage.remove('apiConfig')
    return this.currentService
  }

  async switchToAPIStorage(config: StorageConfig): Promise<StorageService> {
    if (!config.apiKey || !config.apiUrl) {
      throw new Error('API配置不完整')
    }

    const isValidKey = await APIStorageService.validateApiKey(config.apiKey, config.apiUrl)
    if (!isValidKey) {
      throw new Error('API Key验证失败')
    }

    this.currentService = new APIStorageService({
      baseUrl: config.apiUrl,
      apiKey: config.apiKey,
      timeout: config.timeout || 10000
    })

    await this.storage.set('apiConfig', config)
    return this.currentService
  }

  async resetStorage(): Promise<void> {
    this.currentService = null
    await this.storage.remove('apiConfig')
  }

  // 数据迁移工具
  async migrateFromLocalToAPI(apiConfig: StorageConfig): Promise<void> {
    if (!this.currentService || !(this.currentService instanceof LocalStorageService)) {
      throw new Error('当前不是本地存储服务')
    }

    const localService = this.currentService as LocalStorageService
    const localData = await localService.exportData()

    // 创建API存储服务
    const apiService = await this.switchToAPIStorage(apiConfig)

    // 迁移数据
    try {
      // 迁移项目
      for (const project of localData.projects) {
        const { id, createdAt, updatedAt, ...projectData } = project
        await apiService.addProject(projectData)
      }

      // 迁移外链
      for (const link of localData.links) {
        const { id, createdAt, updatedAt, ...linkData } = link
        await apiService.addLink(linkData)
      }

      // 迁移设置
      await apiService.updateSettings(localData.settings)

      console.log('数据迁移完成')
    } catch (error) {
      console.error('数据迁移失败:', error)
      throw error
    }
  }

  async migrateFromAPIToLocal(): Promise<void> {
    if (!this.currentService || !(this.currentService instanceof APIStorageService)) {
      throw new Error('当前不是API存储服务')
    }

    const apiService = this.currentService as APIStorageService

    // 获取API数据
    const [projects, links, settings] = await Promise.all([
      apiService.getProjects(),
      apiService.getLinks(),
      apiService.getSettings()
    ])

    // 切换到本地存储
    const localService = await this.switchToLocalStorage() as LocalStorageService

    // 导入数据
    try {
      await localService.importData({ projects, links, settings })
      console.log('数据迁移完成')
    } catch (error) {
      console.error('数据迁移失败:', error)
      throw error
    }
  }
}