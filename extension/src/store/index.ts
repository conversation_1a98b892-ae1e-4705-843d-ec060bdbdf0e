import { create } from 'zustand'
import type { 
  Project, 
  ExternalLink, 
  UserSettings, 
  FormElement 
} from '~/types'
import { ProjectManager, LinkManager } from '~/services/managers'
import { SettingsManager } from '~/services/managers/settings-manager'
import { AIService } from '~/services/ai'
import { StorageFactory } from '~/services/storage'
import { errorHandler, showToast, type Toast, type AppError } from '~/lib/error-handler'

interface AppState {
  // 用户设置
  settings: UserSettings
  isLoading: boolean
  error: string | null

  // 项目管理
  projects: Project[]
  selectedProject: Project | null
  projectsLoading: boolean

  // 外链管理
  links: ExternalLink[]
  selectedLink: ExternalLink | null
  linksLoading: boolean

  // 表单检测
  detectedForms: any[]
  selectedForm: any | null
  formDetectionLoading: boolean

  // AI优化
  aiService: AIService | null
  aiOptimizing: boolean

  // 错误和通知管理
  toasts: Toast[]
  errors: AppError[]

  // UI状态
  currentView: 'dashboard' | 'projects' | 'links' | 'forms' | 'settings'
  sidebarVisible: boolean

  // Actions
  initializeApp: () => Promise<void>
  
  // 设置相关
  updateSettings: (updates: Partial<UserSettings>) => Promise<void>
  
  // 项目相关
  loadProjects: () => Promise<void>
  addProject: (project: any) => Promise<void>
  updateProject: (id: string, updates: Partial<Project>) => Promise<void>
  deleteProject: (id: string) => Promise<void>
  selectProject: (project: Project | null) => void
  
  // 外链相关
  loadLinks: () => Promise<void>
  addLink: (link: any) => Promise<void>
  updateLink: (id: string, updates: Partial<ExternalLink>) => Promise<void>
  deleteLink: (id: string) => Promise<void>
  selectLink: (link: ExternalLink | null) => void
  saveCurrentUrl: () => Promise<void>
  
  // 表单相关
  detectForms: () => Promise<void>
  selectForm: (form: any | null) => void
  fillForm: (formIndex: number, project: Project, mapping?: any) => Promise<any>
  
  // AI相关
  optimizeContent: (content: string, context: any) => Promise<string>
  
  // UI相关
  setCurrentView: (view: AppState['currentView']) => void
  setSidebarVisible: (visible: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
  
  // 错误和通知管理
  addToast: (toast: Omit<Toast, 'id'>) => string
  removeToast: (id: string) => void
  clearAllToasts: () => void
  handleError: (error: any, context?: string) => AppError
  retryError: (errorId: string, retryFn: () => Promise<any>) => Promise<boolean>
  clearAllErrors: () => void
}

export const useAppStore = create<AppState>((set, get) => {
  const projectManager = new ProjectManager()
  const linkManager = new LinkManager()
  const settingsManager = new SettingsManager()
  let aiService: AIService | null = null

  // 初始化错误处理监听器
  const errorUnsubscribe = errorHandler.onError((error: AppError) => {
    set(state => ({ errors: [...state.errors, error] }))
  })

  const toastUnsubscribe = errorHandler.onToast((toast: Toast) => {
    set(state => ({ toasts: [...state.toasts, toast] }))
  })

  return {
    // 初始状态
    settings: {
      isPaidUser: false,
      theme: 'system',
      language: 'zh-CN',
      autoOpenSidebar: false,
      aiOptimizationEnabled: true
    },
    isLoading: false,
    error: null,

    projects: [],
    selectedProject: null,
    projectsLoading: false,

    links: [],
    selectedLink: null,
    linksLoading: false,

    detectedForms: [],
    selectedForm: null,
    formDetectionLoading: false,

    aiService: null,
    aiOptimizing: false,

    toasts: [],
    errors: [],

    currentView: 'dashboard',
    sidebarVisible: true,

    // Actions
    initializeApp: async () => {
      set({ isLoading: true, error: null })
      
      try {
        // 初始化存储服务
        const storageFactory = StorageFactory.getInstance()
        const storage = await storageFactory.getStorageService()
        
        // 加载用户设置
        const settings = await storage.getSettings()
        set({ settings })

        // 初始化AI服务
        aiService = new AIService()
        set({ aiService })

        // 并行加载项目和外链数据
        await Promise.all([
          get().loadProjects(),
          get().loadLinks()
        ])

        console.log('应用初始化完成')
      } catch (error) {
        console.error('应用初始化失败:', error)
        set({ error: error instanceof Error ? error.message : '初始化失败' })
      } finally {
        set({ isLoading: false })
      }
    },

    // 设置相关
    updateSettings: async (updates) => {
      try {
        const newSettings = await settingsManager.updateSettings(updates)
        set({ settings: newSettings })
        
        // 如果更新了用户类型，重新初始化服务
        if (updates.isPaidUser !== undefined) {
          aiService = new AIService()
          set({ aiService })
        }
        
        showToast.success('设置已保存')
      } catch (error) {
        get().handleError(error, 'settings')
        throw error
      }
    },

    // 项目相关
    loadProjects: async () => {
      set({ projectsLoading: true })
      
      try {
        const projects = await projectManager.getProjects()
        set({ projects })
      } catch (error) {
        console.error('加载项目失败:', error)
        set({ error: error instanceof Error ? error.message : '加载项目失败' })
      } finally {
        set({ projectsLoading: false })
      }
    },

    addProject: async (projectData) => {
      try {
        const newProject = await projectManager.addProject(projectData)
        set(state => ({ 
          projects: [newProject, ...state.projects],
          selectedProject: newProject
        }))
      } catch (error) {
        console.error('添加项目失败:', error)
        set({ error: error instanceof Error ? error.message : '添加项目失败' })
        throw error
      }
    },

    updateProject: async (id, updates) => {
      try {
        const updatedProject = await projectManager.updateProject(id, updates)
        set(state => ({
          projects: state.projects.map(p => p.id === id ? updatedProject : p),
          selectedProject: state.selectedProject?.id === id ? updatedProject : state.selectedProject
        }))
      } catch (error) {
        console.error('更新项目失败:', error)
        set({ error: error instanceof Error ? error.message : '更新项目失败' })
        throw error
      }
    },

    deleteProject: async (id) => {
      try {
        await projectManager.deleteProject(id)
        set(state => ({
          projects: state.projects.filter(p => p.id !== id),
          selectedProject: state.selectedProject?.id === id ? null : state.selectedProject
        }))
      } catch (error) {
        console.error('删除项目失败:', error)
        set({ error: error instanceof Error ? error.message : '删除项目失败' })
        throw error
      }
    },

    selectProject: (project) => {
      set({ selectedProject: project })
    },

    // 外链相关
    loadLinks: async () => {
      set({ linksLoading: true })
      
      try {
        const links = await linkManager.getLinks()
        set({ links })
      } catch (error) {
        console.error('加载外链失败:', error)
        set({ error: error instanceof Error ? error.message : '加载外链失败' })
      } finally {
        set({ linksLoading: false })
      }
    },

    addLink: async (linkData) => {
      try {
        const newLink = await linkManager.addLink(linkData)
        set(state => ({ 
          links: [newLink, ...state.links],
          selectedLink: newLink
        }))
      } catch (error) {
        console.error('添加外链失败:', error)
        set({ error: error instanceof Error ? error.message : '添加外链失败' })
        throw error
      }
    },

    updateLink: async (id, updates) => {
      try {
        const updatedLink = await linkManager.updateLink(id, updates)
        set(state => ({
          links: state.links.map(l => l.id === id ? updatedLink : l),
          selectedLink: state.selectedLink?.id === id ? updatedLink : state.selectedLink
        }))
      } catch (error) {
        console.error('更新外链失败:', error)
        set({ error: error instanceof Error ? error.message : '更新外链失败' })
        throw error
      }
    },

    deleteLink: async (id) => {
      try {
        await linkManager.deleteLink(id)
        set(state => ({
          links: state.links.filter(l => l.id !== id),
          selectedLink: state.selectedLink?.id === id ? null : state.selectedLink
        }))
      } catch (error) {
        console.error('删除外链失败:', error)
        set({ error: error instanceof Error ? error.message : '删除外链失败' })
        throw error
      }
    },

    selectLink: (link) => {
      set({ selectedLink: link })
    },

    saveCurrentUrl: async () => {
      try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
        if (tab.url) {
          const newLink = await linkManager.saveCurrentUrl(tab.url)
          set(state => ({ 
            links: [newLink, ...state.links],
            selectedLink: newLink
          }))
        }
      } catch (error) {
        console.error('保存当前网址失败:', error)
        set({ error: error instanceof Error ? error.message : '保存当前网址失败' })
        throw error
      }
    },

    // 表单相关
    detectForms: async () => {
      set({ formDetectionLoading: true })
      
      try {
        // 发送消息到content script
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
        if (!tab.id) throw new Error('无法获取当前标签页')

        const response = await chrome.tabs.sendMessage(tab.id, { type: 'DETECT_FORMS' })
        
        if (response.success) {
          set({ detectedForms: response.forms })
        } else {
          throw new Error(response.error)
        }
      } catch (error) {
        console.error('检测表单失败:', error)
        set({ error: error instanceof Error ? error.message : '检测表单失败' })
      } finally {
        set({ formDetectionLoading: false })
      }
    },

    selectForm: (form) => {
      set({ selectedForm: form })
    },

    fillForm: async (formIndex, project, mapping) => {
      try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
        if (!tab.id) throw new Error('无法获取当前标签页')

        const response = await chrome.tabs.sendMessage(tab.id, {
          type: 'FILL_FORM',
          formIndex,
          project,
          mapping
        })

        if (response.success) {
          return response.result
        } else {
          throw new Error(response.error)
        }
      } catch (error) {
        console.error('填充表单失败:', error)
        set({ error: error instanceof Error ? error.message : '填充表单失败' })
        throw error
      }
    },

    // AI相关
    optimizeContent: async (content, context) => {
      if (!aiService) {
        throw new Error('AI服务未初始化')
      }

      set({ aiOptimizing: true })
      
      try {
        const optimized = await aiService.optimizeContent(content, context)
        return optimized
      } catch (error) {
        console.error('内容优化失败:', error)
        set({ error: error instanceof Error ? error.message : '内容优化失败' })
        throw error
      } finally {
        set({ aiOptimizing: false })
      }
    },

    // UI相关
    setCurrentView: (view) => {
      set({ currentView: view })
    },

    setSidebarVisible: (visible) => {
      set({ sidebarVisible: visible })
    },

    setError: (error) => {
      set({ error })
    },

    clearError: () => {
      set({ error: null })
    },

    // 错误和通知管理
    addToast: (toast) => {
      return errorHandler.showToast(toast)
    },

    removeToast: (id) => {
      set(state => ({ toasts: state.toasts.filter(t => t.id !== id) }))
    },

    clearAllToasts: () => {
      set({ toasts: [] })
    },

    handleError: (error, context) => {
      return errorHandler.handleError(error, context)
    },

    retryError: async (errorId, retryFn) => {
      return await errorHandler.retryError(errorId, retryFn)
    },

    clearAllErrors: () => {
      errorHandler.clearAllErrors()
      set({ errors: [] })
    }
  }
})