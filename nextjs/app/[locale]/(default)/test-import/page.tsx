"use client";

import { LinkResourcesImportDialog } from "@/components/blocks/link-dashboard/link-resources-import-dialog";

export default function TestImportPage() {
  const handleImportComplete = () => {
    console.log("Import completed!");
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-8">Test CSV Import</h1>
      <LinkResourcesImportDialog 
        onImportComplete={handleImportComplete}
        trigger={
          <button className="bg-blue-500 text-white px-4 py-2 rounded">
            Test Import Dialog
          </button>
        }
      />
    </div>
  );
}
