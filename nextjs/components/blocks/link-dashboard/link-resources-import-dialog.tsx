"use client";

import { useState, useRef, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Upload,
  Download,
  FileText,
  AlertCircle,
  CheckCircle,
  Search,
  ArrowL<PERSON><PERSON>,
  Loader2
} from "lucide-react";
import { toast } from "sonner";

// 数据结构定义
interface ParsedLinkResource {
  url: string;
  title: string;
  link_type: string;
  price?: string;
  source?: string;
  acquisition_method?: string;
  notes?: string;
  submit_url?: string;

  // 前端状态
  _index: number;
  _selected: boolean;
  _status: 'valid' | 'duplicate' | 'error';
  _errors: string[];
  _isDuplicate: boolean;
  _duplicateWith?: string;
}

enum ImportStage {
  UPLOAD = 'upload',
  PREVIEW = 'preview',
  IMPORTING = 'importing',
  COMPLETE = 'complete'
}

interface ImportProgress {
  current: number;
  total: number;
  stage: 'checking' | 'importing';
  successful: number;
  failed: number;
  errors: string[];
}

interface LinkResourcesImportDialogProps {
  onImportComplete: () => void;
  trigger?: React.ReactNode;
}

export function LinkResourcesImportDialog({ onImportComplete, trigger }: LinkResourcesImportDialogProps) {
  const [open, setOpen] = useState(false);
  const [stage, setStage] = useState<ImportStage>(ImportStage.UPLOAD);
  const [file, setFile] = useState<File | null>(null);
  const [parsedData, setParsedData] = useState<ParsedLinkResource[]>([]);
  const [filteredData, setFilteredData] = useState<ParsedLinkResource[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<'all' | 'valid' | 'duplicate' | 'error'>('all');
  const [importing, setImporting] = useState(false);
  const [progress, setProgress] = useState<ImportProgress | null>(null);
  const [importResult, setImportResult] = useState<{
    success: number;
    failed: number;
    errors: string[];
  } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 监听搜索和过滤条件变化
  useEffect(() => {
    setFilteredData(filterData(parsedData, searchTerm, statusFilter));
  }, [parsedData, searchTerm, statusFilter]);

  const csvTemplate = `url,title,link_type,price,source,acquisition_method,notes,submit_url
https://example.com/page1,Example Article 1,free,,Guest Post,Outreach,High quality blog,https://example.com/submit
https://example.com/page2,Example Article 2,paid,50.00,Directory,Paid Listing,Business directory,
https://example.com/page3,Example Resource,"free,paid",,Resource Page,HARO,Listed in resources section,https://example.com/contact`;

  const downloadTemplate = () => {
    const blob = new Blob([csvTemplate], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'link-resources-template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      if (selectedFile.type !== 'text/csv' && !selectedFile.name.endsWith('.csv')) {
        toast.error("Please select a CSV file");
        return;
      }
      setFile(selectedFile);
      setImportResult(null);

      // 自动处理文件
      await handleFileProcess(selectedFile);
    }
  };

  const parseCSV = (text: string): ParsedLinkResource[] => {
    const lines = text.split('\n').filter(line => line.trim());
    if (lines.length < 2) return [];

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const data: ParsedLinkResource[] = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
      const row: any = {};

      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });

      // 验证必填字段
      const errors: string[] = [];
      if (!row.url) errors.push('URL is required');
      if (!row.title) errors.push('Title is required');

      // 验证URL格式
      if (row.url) {
        try {
          new URL(row.url);
        } catch {
          errors.push('Invalid URL format');
        }
      }

      // 验证link_type
      if (row.link_type && !['free', 'paid', 'free,paid'].includes(row.link_type)) {
        errors.push('Invalid link_type. Use "free", "paid", or "free,paid"');
      }

      // 验证价格
      if (row.price && isNaN(parseFloat(row.price))) {
        errors.push('Invalid price format');
      }

      const parsedRow: ParsedLinkResource = {
        url: row.url || '',
        title: row.title || '',
        link_type: row.link_type || 'free',
        price: row.price || undefined,
        source: row.source || undefined,
        acquisition_method: row.acquisition_method || undefined,
        notes: row.notes || undefined,
        submit_url: row.submit_url || undefined,

        _index: i,
        _selected: errors.length === 0, // 默认选中有效的行
        _status: errors.length === 0 ? 'valid' : 'error',
        _errors: errors,
        _isDuplicate: false,
      };

      data.push(parsedRow);
    }

    return data;
  };

  // 检测重复链接
  const checkDuplicates = async (data: ParsedLinkResource[]): Promise<ParsedLinkResource[]> => {
    try {
      const urls = data.map(item => item.url).filter(Boolean);
      const response = await fetch('/api/link-resources/check-duplicates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ urls })
      });

      if (response.ok) {
        const { duplicates } = await response.json();
        return data.map(item => {
          const isDuplicate = duplicates.includes(item.url);
          return {
            ...item,
            _isDuplicate: isDuplicate,
            _status: isDuplicate ? 'duplicate' : item._status,
            _selected: isDuplicate ? false : item._selected, // 取消选中重复项
            _duplicateWith: isDuplicate ? item.url : undefined
          };
        });
      }
    } catch (error) {
      console.error('Error checking duplicates:', error);
    }
    return data;
  };

  // 过滤数据
  const filterData = (data: ParsedLinkResource[], search: string, status: string) => {
    return data.filter(item => {
      const matchesSearch = !search ||
        item.url.toLowerCase().includes(search.toLowerCase()) ||
        item.title.toLowerCase().includes(search.toLowerCase()) ||
        (item.source && item.source.toLowerCase().includes(search.toLowerCase()));

      const matchesStatus = status === 'all' || item._status === status;

      return matchesSearch && matchesStatus;
    });
  };

  // 处理文件解析
  const handleFileProcess = async (file: File) => {
    try {
      const text = await file.text();
      const parsed = parseCSV(text);

      if (parsed.length === 0) {
        toast.error("No valid data found in CSV file");
        return;
      }

      // 检测重复
      const withDuplicates = await checkDuplicates(parsed);
      setParsedData(withDuplicates);
      setFilteredData(filterData(withDuplicates, searchTerm, statusFilter));
      setStage(ImportStage.PREVIEW);

      toast.success(`Parsed ${parsed.length} rows from CSV`);
    } catch (error) {
      console.error('Error processing file:', error);
      toast.error("Failed to process CSV file");
    }
  };

  // 批量选择功能
  const handleSelectAll = (checked: boolean) => {
    const updated = parsedData.map(item => ({
      ...item,
      _selected: checked && item._status === 'valid' // 只选择有效的项目
    }));
    setParsedData(updated);
    setFilteredData(filterData(updated, searchTerm, statusFilter));
  };

  const handleSelectItem = (index: number, checked: boolean) => {
    const updated = parsedData.map(item =>
      item._index === index ? { ...item, _selected: checked } : item
    );
    setParsedData(updated);
    setFilteredData(filterData(updated, searchTerm, statusFilter));
  };

  const handleSelectValid = () => {
    const updated = parsedData.map(item => ({
      ...item,
      _selected: item._status === 'valid'
    }));
    setParsedData(updated);
    setFilteredData(filterData(updated, searchTerm, statusFilter));
  };

  // 搜索和过滤
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setFilteredData(filterData(parsedData, value, statusFilter));
  };

  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value as any);
    setFilteredData(filterData(parsedData, searchTerm, value));
  };

  // 编辑字段
  const handleEditField = (index: number, field: string, value: string) => {
    const updated = parsedData.map(item => {
      if (item._index === index) {
        const updatedItem = { ...item, [field]: value };

        // 重新验证该行
        const errors: string[] = [];

        // 验证必填字段
        if (!updatedItem.url) errors.push('URL is required');
        if (!updatedItem.title) errors.push('Title is required');

        // 验证URL格式
        if (updatedItem.url) {
          try {
            new URL(updatedItem.url);
          } catch {
            errors.push('Invalid URL format');
          }
        }

        // 验证link_type
        if (updatedItem.link_type && !['free', 'paid', 'free,paid'].includes(updatedItem.link_type)) {
          errors.push('Invalid link_type. Use "free", "paid", or "free,paid"');
        }

        // 验证价格
        if (updatedItem.price && isNaN(parseFloat(updatedItem.price))) {
          errors.push('Invalid price format');
        }

        // 验证submit_url
        if (updatedItem.submit_url) {
          try {
            new URL(updatedItem.submit_url);
          } catch {
            errors.push('Invalid submit URL format');
          }
        }

        // 更新状态
        updatedItem._errors = errors;
        updatedItem._status = errors.length === 0 ? 'valid' : 'error';

        // 如果编辑的是URL，需要重新检查重复状态
        if (field === 'url') {
          updatedItem._isDuplicate = false;
          updatedItem._status = errors.length === 0 ? 'valid' : 'error';

          // 可以在这里添加防抖的实时重复检查
          // 暂时先重置重复状态，用户可以手动重新检查
        }

        return updatedItem;
      }
      return item;
    });

    setParsedData(updated);
    setFilteredData(filterData(updated, searchTerm, statusFilter));
  };

  // 重新检查重复
  const handleRecheckDuplicates = async () => {
    setImporting(true);
    try {
      const withDuplicates = await checkDuplicates(parsedData);
      setParsedData(withDuplicates);
      setFilteredData(filterData(withDuplicates, searchTerm, statusFilter));
      toast.success("Duplicate check completed");
    } catch (error) {
      toast.error("Failed to check duplicates");
    } finally {
      setImporting(false);
    }
  };

  // 导入选中的数据
  const handleImport = async () => {
    const selectedItems = parsedData.filter(item => item._selected);

    if (selectedItems.length === 0) {
      toast.error("Please select at least one item to import");
      return;
    }

    setStage(ImportStage.IMPORTING);
    setImporting(true);
    setProgress({
      current: 0,
      total: selectedItems.length,
      stage: 'importing',
      successful: 0,
      failed: 0,
      errors: []
    });

    try {
      // 转换为API期望的格式
      const linkResources = selectedItems.map(item => ({
        url: item.url,
        title: item.title,
        link_type: item.link_type,
        price: item.price ? parseFloat(item.price) : undefined,
        source: item.source,
        acquisition_method: item.acquisition_method,
        notes: item.notes,
        submit_url: item.submit_url
      }));

      const response = await fetch("/api/link-resources", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          bulk: true,
          linkResources,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setImportResult({
          success: result.success || 0,
          failed: result.failed || 0,
          errors: result.errors || [],
        });

        setStage(ImportStage.COMPLETE);

        if (result.success > 0) {
          toast.success(`Successfully imported ${result.success} link resources`);
        }

        if (result.failed > 0) {
          toast.error(`${result.failed} link resources failed to import`);
        }
      } else {
        toast.error(result.error || "Import failed");
        setImportResult({
          success: 0,
          failed: selectedItems.length,
          errors: Array.isArray(result.details) ? result.details : [result.details || result.error],
        });
        setStage(ImportStage.COMPLETE);
      }
    } catch (error) {
      console.error("Error importing link resources:", error);
      toast.error("Failed to import link resources");
      setImportResult({
        success: 0,
        failed: selectedItems.length,
        errors: ["Network error or server unavailable"],
      });
      setStage(ImportStage.COMPLETE);
    } finally {
      setImporting(false);
      setProgress(null);
    }
  };

  // 重置所有状态
  const resetState = () => {
    setStage(ImportStage.UPLOAD);
    setFile(null);
    setParsedData([]);
    setFilteredData([]);
    setSearchTerm("");
    setStatusFilter('all');
    setImporting(false);
    setProgress(null);
    setImportResult(null);
  };

  const handleClose = () => {
    if (importResult && importResult.success > 0) {
      onImportComplete();
    }
    setOpen(false);
    resetState();
  };

  const handleBack = () => {
    if (stage === ImportStage.PREVIEW) {
      setStage(ImportStage.UPLOAD);
    }
  };

  // 统计信息
  const getStats = () => {
    const total = parsedData.length;
    const valid = parsedData.filter(item => item._status === 'valid').length;
    const duplicate = parsedData.filter(item => item._status === 'duplicate').length;
    const error = parsedData.filter(item => item._status === 'error').length;
    const selected = parsedData.filter(item => item._selected).length;

    return { total, valid, duplicate, error, selected };
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline">
            <Upload className="h-4 w-4 mr-2" />
            Import CSV
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className={stage === ImportStage.PREVIEW ? "max-w-[95vw] w-full" : "sm:max-w-[600px]"}>
        <DialogHeader>
          <DialogTitle>
            {stage === ImportStage.UPLOAD && "Import Link Resources"}
            {stage === ImportStage.PREVIEW && "Preview Import Data"}
            {stage === ImportStage.IMPORTING && "Importing..."}
            {stage === ImportStage.COMPLETE && "Import Complete"}
          </DialogTitle>
          <DialogDescription>
            {stage === ImportStage.UPLOAD && "Import your link resources from a CSV file"}
            {stage === ImportStage.PREVIEW && "Review and select data to import"}
            {stage === ImportStage.IMPORTING && "Please wait while we import your data"}
            {stage === ImportStage.COMPLETE && "Import operation completed"}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {stage === ImportStage.UPLOAD && renderUploadStage()}
          {stage === ImportStage.PREVIEW && renderPreviewStage()}
          {stage === ImportStage.IMPORTING && renderImportingStage()}
          {stage === ImportStage.COMPLETE && renderCompleteStage()}
        </div>
      </DialogContent>
    </Dialog>
  );

  // 渲染上传阶段
  function renderUploadStage() {
    return (
      <>
        {/* CSV Format Info */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <FileText className="h-4 w-4" />
              <h4 className="font-medium">CSV Format</h4>
            </div>
            <div className="text-sm text-muted-foreground space-y-1">
              <p><strong>Required columns:</strong> url, title</p>
              <p><strong>Optional columns:</strong> link_type, price, source, acquisition_method, notes, submit_url</p>
              <p><strong>Link type format:</strong> Use "free", "paid", or "free,paid" for multiple types</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={downloadTemplate}
              className="mt-2"
            >
              <Download className="h-4 w-4 mr-2" />
              Download Template
            </Button>
          </CardContent>
        </Card>

        {/* File Upload */}
        <div className="space-y-2">
          <Label htmlFor="csvFile">Upload CSV File</Label>
          <div
            className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
            {file ? (
              <div>
                <p className="font-medium">{file.name}</p>
                <p className="text-sm text-muted-foreground">
                  {(file.size / 1024).toFixed(1)} KB
                </p>
              </div>
            ) : (
              <div>
                <p className="font-medium">Click to select CSV file</p>
                <p className="text-sm text-muted-foreground">
                  Or drag and drop your file here
                </p>
              </div>
            )}
          </div>
          <Input
            ref={fileInputRef}
            type="file"
            accept=".csv"
            onChange={handleFileChange}
            className="hidden"
          />
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
        </div>
      </>
    );
  }

  // 渲染预览阶段
  function renderPreviewStage() {
    const stats = getStats();

    return (
      <>
        {/* 统计信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Import Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{stats.total}</div>
                <div className="text-sm text-muted-foreground">Total</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{stats.valid}</div>
                <div className="text-sm text-muted-foreground">Valid</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{stats.duplicate}</div>
                <div className="text-sm text-muted-foreground">Duplicate</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{stats.error}</div>
                <div className="text-sm text-muted-foreground">Error</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.selected}</div>
                <div className="text-sm text-muted-foreground">Selected</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 过滤和搜索 */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by URL, title, or source..."
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="valid">Valid</SelectItem>
              <SelectItem value="duplicate">Duplicate</SelectItem>
              <SelectItem value="error">Error</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 批量操作 */}
        <div className="flex gap-2 flex-wrap">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleSelectAll(true)}
          >
            Select All Valid
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleSelectAll(false)}
          >
            Deselect All
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleSelectValid}
          >
            Select Only Valid
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRecheckDuplicates}
            disabled={importing}
          >
            Recheck Duplicates
          </Button>
        </div>

        {/* 数据表格 */}
        <div className="border rounded-lg max-h-[60vh] overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={stats.selected === stats.valid && stats.valid > 0}
                    onCheckedChange={(checked) => handleSelectAll(checked as boolean)}
                  />
                </TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="min-w-[200px]">URL</TableHead>
                <TableHead>Title</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Source</TableHead>
                <TableHead>Submit URL</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.map((item) => (
                <TableRow key={item._index}>
                  <TableCell>
                    <Checkbox
                      checked={item._selected}
                      onCheckedChange={(checked) => handleSelectItem(item._index, checked as boolean)}
                      disabled={item._status === 'error'}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Badge
                        variant={
                          item._status === 'valid' ? 'default' :
                          item._status === 'duplicate' ? 'secondary' : 'destructive'
                        }
                      >
                        {item._status}
                      </Badge>
                      {item._errors.length > 0 && (
                        <div className="text-xs text-red-600 space-y-1">
                          {item._errors.map((error, idx) => (
                            <div key={idx} className="truncate" title={error}>
                              {error}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Input
                      value={item.url}
                      onChange={(e) => handleEditField(item._index, 'url', e.target.value)}
                      className={`text-sm ${item._errors.some(e => e.includes('URL')) ? 'border-red-500' : ''}`}
                      placeholder="Enter URL"
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      value={item.title}
                      onChange={(e) => handleEditField(item._index, 'title', e.target.value)}
                      className={`text-sm ${item._errors.some(e => e.includes('Title')) ? 'border-red-500' : ''}`}
                      placeholder="Enter title"
                    />
                  </TableCell>
                  <TableCell>
                    <Select
                      value={item.link_type}
                      onValueChange={(value) => handleEditField(item._index, 'link_type', value)}
                    >
                      <SelectTrigger className="w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="free">Free</SelectItem>
                        <SelectItem value="paid">Paid</SelectItem>
                        <SelectItem value="free,paid">Both</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <Input
                      value={item.price || ''}
                      onChange={(e) => handleEditField(item._index, 'price', e.target.value)}
                      className="text-sm w-20"
                      placeholder="0.00"
                      type="number"
                      step="0.01"
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      value={item.source || ''}
                      onChange={(e) => handleEditField(item._index, 'source', e.target.value)}
                      className="text-sm w-32"
                      placeholder="Source"
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      value={item.submit_url || ''}
                      onChange={(e) => handleEditField(item._index, 'submit_url', e.target.value)}
                      className="text-sm min-w-[150px]"
                      placeholder="Submit URL"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <Button
            onClick={handleImport}
            disabled={stats.selected === 0}
          >
            Import {stats.selected} Items
          </Button>
        </div>
      </>
    );
  }

  // 渲染导入进度阶段
  function renderImportingStage() {
    return (
      <>
        <div className="space-y-4">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-lg font-medium">Importing your data...</p>
            <p className="text-sm text-muted-foreground">Please wait while we process your link resources</p>
          </div>

          {progress && (
            <div className="space-y-2">
              <Progress value={(progress.current / progress.total) * 100} />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>
                  {progress.stage === 'importing' && `Importing ${progress.current} of ${progress.total} items`}
                </span>
                <span>{Math.round((progress.current / progress.total) * 100)}%</span>
              </div>
              <div className="flex justify-center gap-4 text-sm">
                <span className="text-green-600">✓ {progress.successful} successful</span>
                <span className="text-red-600">✗ {progress.failed} failed</span>
              </div>
            </div>
          )}
        </div>
      </>
    );
  }

  // 渲染完成阶段
  function renderCompleteStage() {
    return (
      <>
        {/* Import Results */}
        {importResult && (
          <div className="space-y-4">
            {importResult.success > 0 && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Successfully imported {importResult.success} link resources
                </AlertDescription>
              </Alert>
            )}

            {importResult.errors.length > 0 && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    <p>Import errors:</p>
                    <ul className="text-sm list-disc list-inside">
                      {importResult.errors.slice(0, 5).map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                      {importResult.errors.length > 5 && (
                        <li>... and {importResult.errors.length - 5} more errors</li>
                      )}
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={handleClose}>
            {importResult && importResult.success > 0 ? "Close" : "Cancel"}
          </Button>
          <Button onClick={() => {
            resetState();
            setStage(ImportStage.UPLOAD);
          }}>
            Import More
          </Button>
        </div>
      </>
    );
  }
}